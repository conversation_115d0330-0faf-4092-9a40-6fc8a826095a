"""
PDF ingestion and processing functionality.
"""

import os
import re
import tempfile
from flask import request
from unstructured.partition.pdf import partition_pdf

from ..core.config import MAX_TOKENS_PER_CHUNK, CHARS_PER_TOKEN, GPU_REST_DELAY
from ..core.utils import (
    generate_session_id, estimate_token_count, allowed_file,
    log_and_return_error, create_error_response, create_success_response
)
from ..core.state import queue_lock, processing_status, chunk_queues
from .n8n_integration import _send_chunk_to_n8n


def find_best_split_point(text, max_position):
    """Find optimal text split position prioritizing natural boundaries."""
    if max_position >= len(text):
        return len(text)

    search_start = max(0, int(max_position * 0.8))

    # Priority order: paragraphs, lines, sentences, words
    patterns = [
        (r'\n\n+', 'paragraph'),
        (r'\n', 'line'),
        (r'[.!?]\s+', 'sentence'),
        (r'\s+', 'word')
    ]

    for pattern, _ in patterns:
        matches = list(re.finditer(pattern, text[search_start:max_position]))
        if matches:
            return search_start + matches[-1].end()

    return max_position


def split_text_for_plan_b(text):
    """Split text into 3 pieces at 33% and 67% points, ensuring splits at sentence/paragraph boundaries."""
    if not text:
        return [], [], []

    # Find split positions at 33% and 67%
    first_split_position = int(len(text) * 0.33)
    second_split_position = int(len(text) * 0.67)

    # Priority order: paragraphs, sentences, lines, words
    patterns = [
        (r'\n\n+', 'paragraph'),
        (r'[.!?]\s+', 'sentence'),
        (r'\n', 'line'),
        (r'\s+', 'word')
    ]

    def find_best_boundary(target_position, search_range=0.1):
        """Find the best boundary near the target position."""
        search_start = max(0, int(target_position * (1 - search_range)))
        search_end = min(len(text), int(target_position * (1 + search_range)))

        for pattern, boundary_type in patterns:
            matches = list(re.finditer(pattern, text[search_start:search_end]))
            if matches:
                best_match = search_start + matches[-1].end()
                return best_match

        return target_position

    # Find optimal split points
    first_split = find_best_boundary(first_split_position)
    second_split = find_best_boundary(second_split_position)

    # Ensure splits are in order and not too close
    if second_split <= first_split + 50:  # Minimum 50 chars between splits
        second_split = first_split + 50
        if second_split >= len(text):
            # If second split goes beyond text, fall back to 2-piece split
            return split_text_into_two_pieces(text)

    # Split the text into 3 pieces
    first_piece = text[:first_split].strip()
    second_piece = text[first_split:second_split].strip()
    third_piece = text[second_split:].strip()

    return first_piece, second_piece, third_piece


def split_text_into_two_pieces(text):
    """Fallback function to split text into 2 pieces if 3-piece split fails."""
    split_position = int(len(text) * 0.5)

    # Find the best split point near 50% position
    search_start = max(0, int(split_position * 0.8))

    patterns = [
        (r'\n\n+', 'paragraph'),
        (r'[.!?]\s+', 'sentence'),
        (r'\n', 'line'),
        (r'\s+', 'word')
    ]

    best_split = split_position
    for pattern, boundary_type in patterns:
        matches = list(re.finditer(pattern, text[search_start:split_position]))
        if matches:
            best_split = search_start + matches[-1].end()
            break

    first_piece = text[:best_split].strip()
    second_piece = text[best_split:].strip()

    return first_piece, second_piece, ""  # Return empty third piece for consistency


def pre_chunk_text(text, max_tokens=MAX_TOKENS_PER_CHUNK):
    """Split text into chunks respecting token limits and natural boundaries."""
    if not text:
        return []

    total_tokens = estimate_token_count(text)

    if total_tokens <= max_tokens:
        return [text]

    chunks = []
    current_position = 0
    chunk_number = 1

    while current_position < len(text):
        max_chars = max_tokens * CHARS_PER_TOKEN
        end_position = min(current_position + max_chars, len(text))
        split_position = find_best_split_point(text, end_position)

        chunk = text[current_position:split_position].strip()

        if chunk:
            chunk_tokens = estimate_token_count(chunk)
            chunks.append(chunk)
            chunk_number += 1

        if split_position > current_position:
            current_position = split_position
        else:
            current_position = min(current_position + max_chars // 2, len(text))

    return chunks


def looks_like_table(text):
    """Detect if text content appears to be a table using heuristics."""
    lines = text.split('\n')
    if len(lines) < 3:
        return False

    table_keywords = ['Technical Specification', 'Interface', 'System', 'Network', 'Storage', 'Display']
    has_keywords = any(keyword in text for keyword in table_keywords)
    has_separators = '|' in text or '\t' in text
    has_multiple_spaces = any('  ' in line for line in lines)
    has_colons = text.count(':') > 3

    return has_keywords or has_separators or (has_multiple_spaces and has_colons)


def process_table_element(element):
    """Process table elements to preserve structure and improve readability."""
    try:
        element_text = str(element)
        lines = element_text.split('\n')

        table_indicators = ['|', '\t', '  ', 'Technical Specification', 'Interface', 'System']
        has_structure = any(indicator in element_text for indicator in table_indicators)

        if has_structure and len(lines) > 2:
            formatted_lines = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                if '|' in line:
                    parts = [part.strip() for part in line.split('|') if part.strip()]
                    if len(parts) >= 2:
                        formatted_lines.append(f"{parts[0]}: {' | '.join(parts[1:])}")
                    else:
                        formatted_lines.append(line)
                elif '\t' in line or '  ' in line:
                    parts = re.split(r'\s{2,}|\t+', line)
                    parts = [part.strip() for part in parts if part.strip()]
                    if len(parts) >= 2:
                        formatted_lines.append(f"{parts[0]}: {' | '.join(parts[1:])}")
                    else:
                        formatted_lines.append(line)
                else:
                    formatted_lines.append(line)

            return "[TABLE]\n" + "\n".join(formatted_lines) + "\n[/TABLE]"
        else:
            return f"[TABLE]\n{element_text}\n[/TABLE]"

    except Exception:
        return f"[TABLE]\n{str(element)}\n[/TABLE]"


def _validate_pdf_request():
    """Validate PDF upload request."""
    if 'pdfFile' not in request.files:
        return create_error_response(
            "No file uploaded",
            400
        )

    file = request.files['pdfFile']

    if file.filename == '':
        return create_error_response(
            "No file selected",
            400
        )

    if not allowed_file(file.filename):
        return create_error_response(
            "Only PDF files are allowed",
            400
        )

    return None


def _extract_pdf_content(temp_file_path):
    """Extract content from PDF using unstructured library."""
    try:
        elements = partition_pdf(
            temp_file_path,
            strategy="hi_res",
            infer_table_structure=True,
            extract_images_in_pdf=True,
            extract_image_block_types=["Image", "Table"]
        )
    except Exception as e:
        elements = partition_pdf(
            temp_file_path,
            strategy="fast",
            infer_table_structure=True,
            extract_images_in_pdf=False
        )

    processed_content = []
    table_count = 0

    for element in elements:
        element_type = str(type(element).__name__)
        element_text = str(element)

        if element_type == "Table":
            processed_content.append(process_table_element(element))
            table_count += 1
        elif element_type == "Image":
            processed_content.append(f"[IMAGE] {element_text}")
        else:
            if looks_like_table(element_text):
                processed_content.append(process_table_element(element))
                table_count += 1
            else:
                processed_content.append(element_text)

    extracted_text = "\n".join(processed_content)
    return extracted_text


def _build_pdf_response(extracted_text, chunk_count, total_tokens, workflow_triggered):
    """Build response data for PDF processing."""
    return {
        "status": "success",
        "message": "PDF processed successfully",
        "text": extracted_text,
        "workflow_triggered": workflow_triggered,
        "text_stats": {
            "total_characters": len(extracted_text),
            "estimated_tokens": total_tokens,
            "chunks_created": chunk_count,
            "chunking_applied": chunk_count > 1
        }
    }


def trigger_n8n_workflow(extracted_text, filename=None):
    """Trigger n8n workflow with extracted text, handling chunking if needed."""
    try:
        text_chunks = pre_chunk_text(extracted_text)
        session_id = generate_session_id()

        if len(text_chunks) == 1:
            return _process_single_chunk(text_chunks[0], filename, session_id)
        else:
            return _process_multiple_chunks(text_chunks, filename, session_id)

    except Exception as e:
        return False


def _process_single_chunk(chunk, filename, session_id):
    """Process a single text chunk."""
    with queue_lock:
        processing_status[session_id] = {
            'status': 'processing',
            'total_chunks': 1,
            'current_chunk': filename,
            'last_completed': None,
            'last_status': None,
            'last_update': None,
            'filename': filename,
            'error': None
        }

    success = _send_chunk_to_n8n(chunk, filename, session_id, 1, original_filename=filename)

    if success:
        with queue_lock:
            processing_status[session_id]['current_chunk'] = filename
        return True
    else:
        with queue_lock:
            if session_id in processing_status:
                del processing_status[session_id]
        return False


def _process_multiple_chunks(chunks, filename, session_id):
    """Process multiple text chunks with queue management."""
    try:
        with queue_lock:
            # Initialize chunk queue for this session
            chunk_queues[session_id] = []

            # Add all chunks except the first one to the queue
            for i, chunk in enumerate(chunks[1:], 2):
                chunk_queues[session_id].append((chunk, f"{filename}_chunk_{i}"))

            # Initialize processing status
            chunk_number = 1
            processing_status[session_id] = {
                'status': 'processing',
                'total_chunks': len(chunks),
                'current_chunk': f"{filename}_chunk_{chunk_number}",
                'processed_chunks': 0,
                'last_completed': None,
                'last_status': None,
                'last_update': None,
                'filename': filename,
                'error': None
            }

        # Send the first chunk immediately
        success = _send_chunk_to_n8n(chunks[0], f"{filename}_chunk_1", session_id, 1, original_filename=filename)

        if success:
            with queue_lock:
                processing_status[session_id]['current_chunk'] = f"{filename}_chunk_1"
            return True
        else:
            # Clean up on failure
            with queue_lock:
                if session_id in processing_status:
                    del processing_status[session_id]
                if session_id in chunk_queues:
                    del chunk_queues[session_id]
            return False

    except Exception as e:
        return False
