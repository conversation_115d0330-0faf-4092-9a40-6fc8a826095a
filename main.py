"""
Main application entry point for the ChromaDB server.
"""

import logging
from flask import Flask, jsonify
from flask_cors import CORS

# Import route modules
from src.routes.pdf_routes import upload_pdf, plan_b_processing, upload_youtube_transcript
from src.routes.chroma_routes import add_to_chroma, query_chroma
from src.routes.api_routes import (
    get_stats, get_all_documents, add_document_api, delete_document_api,
    delete_documents_by_source, search_documents, get_collection_info, health_check
)
from src.routes.callback_routes import (
    chunk_processing_complete, plan_b_piece_complete,
    get_chunk_status_route, get_plan_b_status_route
)
from src.routes.admin_routes import (
    print_all, delete_all, web_interface, export_data,
    get_gpu_delay, set_gpu_delay, get_onfail_webhook, set_onfail_webhook
)


def create_app():
    """Create and configure the Flask application."""
    # Application setup
    app = Flask(__name__, static_folder='.', static_url_path='')
    CORS(app)

    # Logging configuration
    logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

    # Application configuration
    app.config['MAX_CONTENT_LENGTH'] = None

    # Initialize global state for debugging
    from src.core.state import plan_b_sessions, queue_lock
    app.logger.info(f"Application started - Plan B sessions initialized: {len(plan_b_sessions)} sessions")

    # Register PDF routes
    @app.route("/upload-pdf", methods=["POST"])
    def upload_pdf_route():
        return upload_pdf(app)

    @app.route("/plan_b", methods=["POST"])
    def plan_b_route():
        return plan_b_processing(app)

    @app.route("/upload-youtube-transcript", methods=["POST"])
    def upload_youtube_transcript_route():
        return upload_youtube_transcript(app)

    # Register ChromaDB routes
    @app.route("/add", methods=["POST"])
    def add_route():
        return add_to_chroma(app)

    @app.route("/query", methods=["POST"])
    def query_route():
        return query_chroma(app)

    # Register API routes
    @app.route("/api/stats", methods=["GET"])
    def stats_route():
        return get_stats(app)

    @app.route("/api/documents", methods=["GET"])
    def documents_route():
        return get_all_documents(app)

    @app.route("/api/add-document", methods=["POST"])
    def add_document_route():
        return add_document_api(app)

    @app.route("/api/delete-document", methods=["POST"])
    def delete_document_route():
        return delete_document_api(app)

    @app.route("/api/documents/by-source", methods=["DELETE"])
    def delete_documents_by_source_alt_route():
        return delete_documents_by_source(app)

    @app.route("/api/search", methods=["POST"])
    def search_route():
        return search_documents(app)

    @app.route("/api/collection-info", methods=["GET"])
    def collection_info_route():
        return get_collection_info(app)

    @app.route("/health", methods=["GET"])
    def health_route():
        return health_check(app)

    # Register callback routes
    @app.route('/chunk_complete', methods=['POST'])
    def chunk_complete_route():
        return chunk_processing_complete(app)

    @app.route('/plan_b_piece_complete', methods=['POST'])
    def plan_b_complete_route():
        return plan_b_piece_complete(app)

    @app.route("/api/chunk-status/<session_id>", methods=["GET"])
    def chunk_status_route(session_id):
        return get_chunk_status_route(session_id, app)

    @app.route("/api/plan-b-status/<session_id>", methods=["GET"])
    def plan_b_status_route(session_id):
        return get_plan_b_status_route(session_id, app)

    # Debug route for Plan B sessions
    @app.route("/api/plan-b-sessions", methods=["GET"])
    def list_plan_b_sessions():
        from src.core.state import plan_b_sessions, queue_lock
        with queue_lock:
            sessions_info = {}
            for session_id, session_data in plan_b_sessions.items():
                sessions_info[session_id] = {
                    'status': session_data.get('status'),
                    'pieces_sent': session_data.get('pieces_sent', 0),
                    'pieces_completed': session_data.get('pieces_completed', 0),
                    'total_pieces': session_data.get('total_pieces', 0),
                    'created_at': session_data.get('created_at').isoformat() if session_data.get('created_at') else None
                }
        return jsonify({
            "total_sessions": len(plan_b_sessions),
            "sessions": sessions_info
        })

    # Register admin routes
    @app.route("/print_all", methods=["POST"])
    def print_all_route():
        return print_all(app)

    @app.route("/delete_all", methods=["POST"])
    def delete_all_route():
        return delete_all(app)

    @app.route("/", methods=["GET"])
    def web_interface_route():
        return web_interface()

    @app.route("/api/export", methods=["GET"])
    def export_route():
        return export_data(app)

    @app.route("/api/gpu-delay", methods=["GET"])
    def gpu_delay_get_route():
        return get_gpu_delay()

    @app.route("/api/gpu-delay", methods=["POST"])
    def gpu_delay_set_route():
        return set_gpu_delay(app)

    @app.route("/api/onfail-webhook", methods=["GET"])
    def onfail_webhook_get_route():
        return get_onfail_webhook()

    @app.route("/api/onfail-webhook", methods=["POST"])
    def onfail_webhook_set_route():
        return set_onfail_webhook(app)

    return app


if __name__ == "__main__":
    app = create_app()
    app.run(host="0.0.0.0", port=5555, debug=True)
