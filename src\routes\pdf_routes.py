"""
PDF upload and processing routes.
"""

import os
import tempfile
from flask import request, jsonify
from datetime import datetime

from ..core.utils import create_error_response, create_success_response
from ..core.state import queue_lock, plan_b_sessions
from ..flows.ingestion import (
    _validate_pdf_request, _extract_pdf_content, _build_pdf_response,
    pre_chunk_text, estimate_token_count, trigger_n8n_workflow,
    split_text_for_plan_b
)
from ..core.utils import estimate_token_count
from ..flows.n8n_integration import _send_chunk_to_onfail_webhook


def upload_pdf(app):
    """Handle PDF upload, validation, and content extraction."""
    try:
        # Validate request
        validation_error = _validate_pdf_request()
        if validation_error:
            return validation_error

        file = request.files['pdfFile']
        app.logger.info(f"Processing PDF: {file.filename}")

        # Process file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            file.save(temp_file.name)
            temp_file_path = temp_file.name

        try:
            extracted_text = _extract_pdf_content(temp_file_path)
            text_chunks = pre_chunk_text(extracted_text)
            total_tokens = estimate_token_count(extracted_text)

            workflow_triggered = trigger_n8n_workflow(extracted_text, file.filename)

            response_data = _build_pdf_response(
                extracted_text, len(text_chunks), total_tokens, workflow_triggered
            )

            if len(text_chunks) > 1:
                response_data["message"] = f"PDF split into {len(text_chunks)} chunks for processing"
                app.logger.info(f"PDF split into {len(text_chunks)} chunks")

            if not workflow_triggered:
                response_data["warning"] = "PDF processed but n8n workflow failed. Check if n8n is running."

            return jsonify(response_data), 200

        except Exception as e:
            app.logger.error(f"Failed to extract PDF content: {str(e)}")
            return jsonify({
                "status": "error",
                "message": f"Failed to process PDF: {str(e)}"
            }), 500

        finally:
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                app.logger.warning(f"Failed to delete temp file: {str(e)}")

    except Exception as e:
        app.logger.error(f"Error in upload_pdf: {str(e)}")
        return jsonify({
            "status": "error",
            "message": "Internal server error occurred"
        }), 500


def plan_b_processing(app):
    """Handle Plan B processing when AI Agent fails - split text and send to OnFail workflow."""
    try:
        data = request.get_json()
        if not data:
            return create_error_response("No JSON data provided", 400, "validation_error")

        # Extract session data
        text = data.get('text')
        session_id = data.get('session_id')
        chunk_number = data.get('chunk_number')
        source = data.get('source')

        if not text:
            return create_error_response("Text is required", 400, "validation_error")

        app.logger.info(f"Plan B processing initiated for session {session_id}, chunk {chunk_number}")

        # Split text into 3 pieces
        first_piece, second_piece, third_piece = split_text_for_plan_b(text)

        # Check if we have valid pieces
        pieces = [p for p in [first_piece, second_piece, third_piece] if p.strip()]

        if len(pieces) < 2:
            app.logger.warning("Plan B split resulted in insufficient pieces, sending original text to OnFail")
            # If split fails, send original text as single piece
            session_data = {
                'session_id': session_id,
                'chunk_number': chunk_number,
                'source': source
            }
            success = _send_chunk_to_onfail_webhook(text, session_data, 1)
            if success:
                return create_success_response({
                    "pieces_sent": 1,
                    "split_successful": False
                }, "Plan B processing completed (no split)")
            else:
                return create_error_response("Failed to send text to OnFail workflow", 500)

        # Create Plan B session tracking
        plan_b_session_id = f"planb_{session_id}_{chunk_number}"
        total_pieces = len(pieces)

        with queue_lock:
            plan_b_sessions[plan_b_session_id] = {
                'original_session_id': session_id,
                'original_chunk_number': chunk_number,
                'source': source,
                'total_pieces': total_pieces,
                'pieces_sent': 0,
                'pieces_completed': 0,
                'pieces': pieces,  # Store all pieces
                'first_piece': first_piece,
                'second_piece': second_piece,
                'third_piece': third_piece if third_piece else None,
                'status': 'processing',
                'created_at': datetime.now()
            }

            # Debug: Log session creation
            app.logger.info(f"Created Plan B session: {plan_b_session_id}")
            app.logger.info(f"Total Plan B sessions now: {len(plan_b_sessions)}")
            app.logger.info(f"Session details: original_session={session_id}, chunk={chunk_number}, pieces={total_pieces}")

        # Send first piece immediately
        session_data = {
            'session_id': plan_b_session_id,
            'chunk_number': f"{chunk_number}_piece_1",
            'source': source
        }

        success_first = _send_chunk_to_onfail_webhook(first_piece, session_data, 1)

        if success_first:
            with queue_lock:
                plan_b_sessions[plan_b_session_id]['pieces_sent'] = 1

            app.logger.info(f"Plan B first piece sent successfully for session {plan_b_session_id}")

            return create_success_response({
                "plan_b_session_id": plan_b_session_id,
                "pieces_sent": 1,
                "total_pieces": total_pieces,
                "split_successful": True,
                "first_piece_length": len(first_piece),
                "second_piece_length": len(second_piece),
                "third_piece_length": len(third_piece) if third_piece else 0
            }, f"Plan B processing initiated - first piece sent ({total_pieces} pieces total)")
        else:
            # Clean up on failure
            with queue_lock:
                if plan_b_session_id in plan_b_sessions:
                    del plan_b_sessions[plan_b_session_id]

            return create_error_response("Failed to send first piece to OnFail workflow", 500)

    except Exception as e:
        app.logger.error(f"Error in plan_b_processing: {str(e)}")
        return create_error_response("Internal server error in Plan B processing", 500)


def upload_youtube_transcript(app):
    """Handle YouTube transcript upload and processing."""
    try:
        # Validate request
        validation_error = _validate_youtube_transcript_request()
        if validation_error:
            return validation_error

        # Get data from request
        data = request.get_json()
        transcript_text = data.get('transcript_text', '').strip()
        video_title = data.get('video_title', 'YouTube Video').strip()
        video_url = data.get('video_url', '').strip()
        source = data.get('source', 'youtube_transcripts').strip()

        app.logger.info(f"Processing YouTube transcript: {video_title}")
        app.logger.info(f"Transcript length: {len(transcript_text)} characters")

        # Process the transcript text (skip extraction, start from chunking)
        text_chunks = pre_chunk_text(transcript_text)
        total_tokens = estimate_token_count(transcript_text)

        # Trigger n8n workflow with the transcript text
        workflow_triggered = trigger_n8n_workflow(transcript_text, video_title)

        # Build response similar to PDF upload
        response_data = _build_youtube_transcript_response(
            transcript_text, len(text_chunks), total_tokens, workflow_triggered,
            video_title, video_url, source
        )

        if len(text_chunks) > 1:
            response_data["message"] = f"YouTube transcript split into {len(text_chunks)} chunks for processing"
            app.logger.info(f"YouTube transcript split into {len(text_chunks)} chunks")

        if not workflow_triggered:
            response_data["warning"] = "Transcript processed but n8n workflow failed. Check if n8n is running."

        return jsonify(response_data), 200

    except Exception as e:
        app.logger.error(f"Unexpected error in upload_youtube_transcript: {str(e)}")
        return create_error_response(f"Unexpected error: {str(e)}", 500)


def _validate_youtube_transcript_request():
    """Validate YouTube transcript upload request."""
    if not request.is_json:
        return create_error_response("Request must be JSON", 400)

    data = request.get_json()
    if not data:
        return create_error_response("No JSON data provided", 400)

    transcript_text = data.get('transcript_text', '').strip()
    if not transcript_text:
        return create_error_response("transcript_text is required and cannot be empty", 400)

    # Check if transcript is too short (likely not useful)
    if len(transcript_text) < 50:
        return create_error_response("Transcript text is too short (minimum 50 characters)", 400)

    # Check if transcript is too long (might cause processing issues)
    max_length = 1000000  # 1MB of text
    if len(transcript_text) > max_length:
        return create_error_response(f"Transcript text is too long (maximum {max_length} characters)", 400)

    return None


def _build_youtube_transcript_response(transcript_text, chunk_count, total_tokens, workflow_triggered,
                                     video_title, video_url, source):
    """Build response data for YouTube transcript upload."""
    return {
        "status": "success",
        "message": "YouTube transcript processed successfully",
        "data": {
            "video_title": video_title,
            "video_url": video_url,
            "source": source,
            "transcript_length": len(transcript_text),
            "estimated_tokens": total_tokens,
            "chunk_count": chunk_count,
            "workflow_triggered": workflow_triggered,
            "processing_method": "youtube_transcript",
            "chunking_applied": chunk_count > 1
        }
    }
