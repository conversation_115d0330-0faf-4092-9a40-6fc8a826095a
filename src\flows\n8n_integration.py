"""
N8N workflow integration and webhook handling.
"""

import requests
from ..core.config import N8N_WEBHOOK_URL, N8N_ONFAIL_WEBHOOK_URL


def _send_chunk_to_n8n(text_chunk, filename=None, session_id=None, chunk_number=None, original_filename=None):
    """Send a single text chunk to n8n webhook."""
    try:
        # Use original filename for source to maintain consistent source naming in ChromaDB
        # The filename parameter may contain chunk identifiers for internal tracking
        source_name = original_filename if original_filename else filename

        # Extract original filename if filename contains chunk identifier
        if filename and "_chunk_" in filename and not original_filename:
            source_name = filename.split("_chunk_")[0]

        payload = {
            "text": text_chunk,
            "source": source_name,  # Always use original filename for ChromaDB storage
            "session_id": session_id,
            "chunk_number": chunk_number,
            "chunk_identifier": filename,  # Keep chunk identifier for tracking
            "callback_url": "http://localhost:5555/chunk_complete"
        }

        response = requests.post(
            N8N_WEBHOOK_URL,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        if response.status_code == 200:
            return True
        else:
            return False

    except requests.exceptions.RequestException as e:
        return False
    except Exception as e:
        return False


def _send_chunk_to_onfail_webhook(text_chunk, session_data, piece_number):
    """Send a text chunk to the OnFail workflow webhook."""
    try:
        payload = {
            "text": text_chunk,
            "source": session_data.get('source'),
            "session_id": session_data.get('session_id'),
            "chunk_number": session_data.get('chunk_number'),
            "plan_b_piece": piece_number,
            "original_session_id": session_data.get('session_id')
        }

        response = requests.post(
            N8N_ONFAIL_WEBHOOK_URL,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        if response.status_code == 200:
            return True
        else:
            return False

    except requests.exceptions.RequestException as e:
        return False
    except Exception as e:
        return False
