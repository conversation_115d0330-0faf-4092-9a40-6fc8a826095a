"""
Core utility functions for the application.
"""

import time
import uuid
from flask import jsonify
from .config import ALLOWED_EXTENSIONS, CHARS_PER_TOKEN


def generate_session_id():
    """Generate a unique session ID for chunk processing."""
    return str(uuid.uuid4())


def estimate_token_count(text):
    """Estimate token count using 4 characters per token approximation."""
    return len(text) // CHARS_PER_TOKEN if text else 0


def allowed_file(filename):
    """Check if the uploaded file has an allowed extension."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def create_error_response(message, status_code=500, error_type="error"):
    """Create standardized error response."""
    return jsonify({
        "status": "error",
        "error_type": error_type,
        "message": message
    }), status_code


def create_success_response(data=None, message="Success"):
    """Create standardized success response."""
    response = {"status": "success", "message": message}
    if data:
        response.update(data)
    return jsonify(response)


def log_and_return_error(logger, logger_msg, user_msg, status_code=500):
    """Log error and return standardized error response."""
    logger.error(logger_msg)
    return create_error_response(user_msg, status_code)
